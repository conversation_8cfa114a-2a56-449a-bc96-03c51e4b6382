#!/usr/bin/env python3
"""
Tushare数据源测试文件
测试tushare数据源的集成是否正常工作
"""

import os
from Chan import CChan
from ChanConfig import CChanConfig
from Common.CEnum import AUTYPE, DATA_SRC, KL_TYPE
from DataAPI.TushareAPI import CTushare

def test_tushare_basic():
    """测试tushare基本功能"""
    print("=== 测试Tushare基本功能 ===")
    
    # 检查环境变量
    token = "ccc2a61681915d745e29a939dcc976023b4fc312d11f2efa77837d33"
    if not token:
        print("警告: 未设置TUSHARE_TOKEN环境变量")
        print("请设置环境变量: export TUSHARE_TOKEN=your_token")
        return False
    
    try:
        # 初始化tushare
        CTushare.do_init()
        print("✓ Tushare初始化成功")
        
        # 测试数据获取
        code = "000001.SZ"  # 平安银行
        begin_time = "2024-01-01"
        end_time = "2024-01-10"
        
        tushare_api = CTushare(
            code=code,
            k_type=KL_TYPE.K_DAY,
            begin_date=begin_time,
            end_date=end_time,
            autype=AUTYPE.QFQ
        )
        
        print(f"✓ 创建Tushare API实例成功")
        print(f"  股票代码: {code}")
        print(f"  股票名称: {tushare_api.name}")
        print(f"  是否为股票: {tushare_api.is_stock}")
        
        # 获取K线数据
        kline_count = 0
        for kline in tushare_api.get_kl_data():
            kline_count += 1
            if kline_count <= 3:  # 只打印前3条数据
                print(f"  K线数据 {kline_count}: {kline.time} O:{kline.open} H:{kline.high} L:{kline.low} C:{kline.close}")
        
        print(f"✓ 成功获取 {kline_count} 条K线数据")
        
        # 关闭连接
        CTushare.do_close()
        print("✓ Tushare连接关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Tushare基本功能测试失败: {e}")
        return False

def test_tushare_with_chan():
    """测试tushare与缠论框架集成"""
    print("\n=== 测试Tushare与缠论框架集成 ===")
    
    # 检查环境变量
    token = "ccc2a61681915d745e29a939dcc976023b4fc312d11f2efa77837d33"
    if not token:
        print("警告: 未设置TUSHARE_TOKEN环境变量，跳过集成测试")
        return False
    
    try:
        code = "000001.SZ"  # 平安银行
        begin_time = "2024-01-01"
        end_time = "2024-02-01"
        data_src = DATA_SRC.TUSHARE
        lv_list = [KL_TYPE.K_DAY]

        config = CChanConfig({
            "bi_strict": True,
            "trigger_step": False,
            "skip_step": 0,
            "divergence_rate": float("inf"),
            "bsp2_follow_1": False,
            "bsp3_follow_1": False,
            "min_zs_cnt": 0,
            "bs1_peak": False,
            "macd_algo": "peak",
            "bs_type": '1,2,3a,1p,2s,3b',
            "print_warning": True,
            "zs_algo": "normal",
        })

        print(f"创建CChan实例...")
        chan = CChan(
            code=code,
            begin_time=begin_time,
            end_time=end_time,
            data_src=data_src,
            lv_list=lv_list,
            config=config,
            autype=AUTYPE.QFQ,
        )
        
        print(f"✓ CChan实例创建成功")
        
        # 获取K线数据统计
        kline_list = chan.get_kl_data()
        if kline_list and len(kline_list) > 0:
            kl_data = kline_list[0]  # 获取第一个级别的K线数据
            print(f"✓ 成功获取K线数据，共 {len(kl_data)} 条")
            
            if len(kl_data) > 0:
                first_kl = kl_data[0]
                last_kl = kl_data[-1]
                print(f"  时间范围: {first_kl.time} 到 {last_kl.time}")
                print(f"  首条K线: O:{first_kl.open} H:{first_kl.high} L:{first_kl.low} C:{first_kl.close}")
                print(f"  末条K线: O:{last_kl.open} H:{last_kl.high} L:{last_kl.low} C:{last_kl.close}")
        
        # 获取笔的统计
        bi_list = chan.get_bi_list()
        if bi_list and len(bi_list) > 0:
            bi_data = bi_list[0]  # 获取第一个级别的笔数据
            print(f"✓ 成功计算笔，共 {len(bi_data)} 笔")
            
            if len(bi_data) > 0:
                first_bi = bi_data[0]
                last_bi = bi_data[-1]
                print(f"  首笔: {first_bi.dir} {first_bi.begin_klc.idx}->{first_bi.end_klc.idx}")
                print(f"  末笔: {last_bi.dir} {last_bi.begin_klc.idx}->{last_bi.end_klc.idx}")
        
        # 获取线段统计
        seg_list = chan.get_seg_list()
        if seg_list and len(seg_list) > 0:
            seg_data = seg_list[0]  # 获取第一个级别的线段数据
            print(f"✓ 成功计算线段，共 {len(seg_data)} 段")
        
        # 获取买卖点统计
        bsp_list = chan.get_bsp_list()
        if bsp_list and len(bsp_list) > 0:
            bsp_data = bsp_list[0]  # 获取第一个级别的买卖点数据
            print(f"✓ 成功计算买卖点，共 {len(bsp_data)} 个")
        
        print("✓ Tushare与缠论框架集成测试成功")
        return True
        
    except Exception as e:
        print(f"✗ Tushare与缠论框架集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_stocks():
    """测试不同股票代码格式"""
    print("\n=== 测试不同股票代码格式 ===")
    
    token = "ccc2a61681915d745e29a939dcc976023b4fc312d11f2efa77837d33"
    if not token:
        print("警告: 未设置TUSHARE_TOKEN环境变量，跳过测试")
        return False
    
    test_codes = [
        "000001.SZ",  # 平安银行（深圳）
        "600000.SH",  # 浦发银行（上海）
        "sz.000001",  # 框架格式（深圳）
        "sh.600000",  # 框架格式（上海）
    ]
    
    try:
        CTushare.do_init()
        
        for code in test_codes:
            try:
                print(f"测试股票代码: {code}")
                tushare_api = CTushare(
                    code=code,
                    k_type=KL_TYPE.K_DAY,
                    begin_date="2024-01-01",
                    end_date="2024-01-05",
                    autype=AUTYPE.QFQ
                )
                
                # 获取一条数据验证
                kline_count = 0
                for kline in tushare_api.get_kl_data():
                    kline_count += 1
                    if kline_count >= 1:  # 只获取一条验证
                        break
                
                print(f"  ✓ {code} -> {tushare_api.name} (获取到 {kline_count} 条数据)")
                
            except Exception as e:
                print(f"  ✗ {code} 测试失败: {e}")
        
        CTushare.do_close()
        return True
        
    except Exception as e:
        print(f"✗ 股票代码格式测试失败: {e}")
        return False

if __name__ == "__main__":
    print("Tushare数据源集成测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import tushare as ts
        print("✓ tushare库已安装")
    except ImportError:
        print("✗ 请先安装tushare库: pip install tushare")
        exit(1)
    
    # 运行测试
    success_count = 0
    total_tests = 3
    
    if test_tushare_basic():
        success_count += 1
    
    if test_tushare_with_chan():
        success_count += 1
    
    if test_different_stocks():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！Tushare数据源集成成功！")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
