import os
from datetime import datetime
from typing import Optional

import tushare as ts

from Common.CEnum import AUTYPE, DATA_FIELD, KL_TYPE
from Common.CTime import CTime
from Common.func_util import kltype_lt_day, str2float
from KLine.KLine_Unit import CKLine_Unit

from .CommonStockAPI import <PERSON>ommonStock<PERSON>pi


def create_item_dict(data, column_name):
    """创建K线数据字典"""
    for i in range(len(data)):
        data[i] = parse_time_column(data[i]) if i == 0 else str2float(data[i])
    return dict(zip(column_name, data))


def parse_time_column(inp):
    """解析时间列，支持多种格式"""
    # tushare返回的时间格式通常是 YYYYMMDD 或 YYYY-MM-DD HH:MM:SS
    if isinstance(inp, str):
        if len(inp) == 8:  # YYYYMMDD
            year = int(inp[:4])
            month = int(inp[4:6])
            day = int(inp[6:8])
            hour = minute = 0
        elif len(inp) == 10:  # YYYY-MM-DD
            year = int(inp[:4])
            month = int(inp[5:7])
            day = int(inp[8:10])
            hour = minute = 0
        elif len(inp) == 19:  # YYYY-MM-DD HH:MM:SS
            year = int(inp[:4])
            month = int(inp[5:7])
            day = int(inp[8:10])
            hour = int(inp[11:13])
            minute = int(inp[14:16])
        else:
            raise Exception(f"unknown time column from tushare:{inp}")
    else:
        raise Exception(f"unsupported time format from tushare:{inp}")
    
    return CTime(year, month, day, hour, minute)


def GetColumnNameFromFieldList(fields: str):
    """将tushare字段名映射到框架字段名"""
    _dict = {
        "trade_date": DATA_FIELD.FIELD_TIME,
        "ts_code": "ts_code",  # 股票代码，不映射到框架字段
        "open": DATA_FIELD.FIELD_OPEN,
        "high": DATA_FIELD.FIELD_HIGH,
        "low": DATA_FIELD.FIELD_LOW,
        "close": DATA_FIELD.FIELD_CLOSE,
        "vol": DATA_FIELD.FIELD_VOLUME,
        "amount": DATA_FIELD.FIELD_TURNOVER,
        "turnover_rate": DATA_FIELD.FIELD_TURNRATE,
        "pre_close": "pre_close",  # 前收盘价，不映射到框架字段
        "change": "change",  # 涨跌额，不映射到框架字段
        "pct_chg": "pct_chg",  # 涨跌幅，不映射到框架字段
    }
    return [_dict.get(x, x) for x in fields.split(",")]


class CTushare(CCommonStockApi):
    """Tushare数据源实现"""
    is_connect = None
    pro = None

    def __init__(self, code, k_type=KL_TYPE.K_DAY, begin_date=None, end_date=None, autype=AUTYPE.QFQ):
        super(CTushare, self).__init__(code, k_type, begin_date, end_date, autype)

    def get_kl_data(self):
        """获取K线数据"""
        # 确保已经初始化
        if not self.pro:
            raise Exception("Tushare未初始化，请先调用CTushare.do_init()方法")
        
        # 转换股票代码格式
        ts_code = self._convert_code_format(self.code)
        
        # 转换日期格式
        start_date = self._convert_date_format(self.begin_date) if self.begin_date else None
        end_date = self._convert_date_format(self.end_date) if self.end_date else None
        
        # 根据K线类型选择不同的数据获取方式
        if kltype_lt_day(self.k_type):
            # 分钟级别数据
            data = self._get_minute_data(ts_code, start_date, end_date)
        else:
            # 日线及以上级别数据
            data = self._get_daily_data(ts_code, start_date, end_date)
        
        # 转换数据格式并返回
        for _, row in data.iterrows():
            item_dict = self._convert_row_to_dict(row)
            yield CKLine_Unit(item_dict)

    def _get_daily_data(self, ts_code, start_date, end_date):
        """获取日线数据"""
        # 使用tushare的pro_bar接口获取日线数据
        freq = self._convert_ktype_to_freq()
        adj = self._convert_autype_to_adj()
        
        data = ts.pro_bar(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            freq=freq,
            adj=adj,
            asset='E'  # 股票
        )
        
        if data is None or data.empty:
            raise Exception(f"未获取到数据: {ts_code}")
        
        # 按日期升序排列（tushare默认是降序）
        data = data.sort_values('trade_date').reset_index(drop=True)
        return data

    def _get_minute_data(self, ts_code, start_date, end_date):
        """获取分钟数据"""
        # 分钟数据需要特殊处理，tushare的分钟数据接口有限制
        freq = self._convert_ktype_to_freq()
        
        # 使用pro_bar接口获取分钟数据
        data = ts.pro_bar(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            freq=freq,
            asset='E'
        )
        
        if data is None or data.empty:
            raise Exception(f"未获取到分钟数据: {ts_code}")
        
        # 按时间升序排列
        data = data.sort_values('trade_date').reset_index(drop=True)
        return data

    def _convert_row_to_dict(self, row):
        """将pandas行数据转换为框架需要的字典格式"""
        item_dict = {
            DATA_FIELD.FIELD_TIME: parse_time_column(str(row['trade_date'])),
            DATA_FIELD.FIELD_OPEN: float(row['open']) if row['open'] is not None else 0.0,
            DATA_FIELD.FIELD_HIGH: float(row['high']) if row['high'] is not None else 0.0,
            DATA_FIELD.FIELD_LOW: float(row['low']) if row['low'] is not None else 0.0,
            DATA_FIELD.FIELD_CLOSE: float(row['close']) if row['close'] is not None else 0.0,
        }
        
        # 添加成交量、成交额、换手率（如果有的话）
        if 'vol' in row and row['vol'] is not None:
            item_dict[DATA_FIELD.FIELD_VOLUME] = float(row['vol'])
        if 'amount' in row and row['amount'] is not None:
            item_dict[DATA_FIELD.FIELD_TURNOVER] = float(row['amount'])
        if 'turnover_rate' in row and row['turnover_rate'] is not None:
            item_dict[DATA_FIELD.FIELD_TURNRATE] = float(row['turnover_rate'])
            
        return item_dict

    def _convert_code_format(self, code):
        """转换股票代码格式"""
        # 框架内部格式可能是 sz.000001 或 sh.600000
        # tushare需要的格式是 000001.SZ 或 600000.SH
        if '.' in code:
            market, stock_code = code.split('.')
            if market.lower() == 'sz':
                return f"{stock_code}.SZ"
            elif market.lower() == 'sh':
                return f"{stock_code}.SH"
            else:
                # 如果已经是tushare格式，直接返回
                return code
        else:
            # 根据股票代码判断市场
            if code.startswith('0') or code.startswith('3'):
                return f"{code}.SZ"
            elif code.startswith('6'):
                return f"{code}.SH"
            else:
                return code

    def _convert_date_format(self, date_str):
        """转换日期格式"""
        if date_str is None:
            return None
        
        # 如果已经是YYYYMMDD格式，直接返回
        if isinstance(date_str, str) and len(date_str) == 8 and date_str.isdigit():
            return date_str
        
        # 如果是YYYY-MM-DD格式，转换为YYYYMMDD
        if isinstance(date_str, str) and len(date_str) == 10:
            return date_str.replace('-', '')
        
        return date_str

    def _convert_ktype_to_freq(self):
        """转换K线类型到tushare频率"""
        _dict = {
            KL_TYPE.K_1M: '1min',
            KL_TYPE.K_3M: '3min',
            KL_TYPE.K_5M: '5min',
            KL_TYPE.K_15M: '15min',
            KL_TYPE.K_30M: '30min',
            KL_TYPE.K_60M: '60min',
            KL_TYPE.K_DAY: 'D',
            KL_TYPE.K_WEEK: 'W',
            KL_TYPE.K_MON: 'M',
        }
        return _dict.get(self.k_type, 'D')

    def _convert_autype_to_adj(self):
        """转换复权类型"""
        _dict = {
            AUTYPE.QFQ: 'qfq',  # 前复权
            AUTYPE.HFQ: 'hfq',  # 后复权
            AUTYPE.NONE: None,  # 不复权
        }
        return _dict.get(self.autype, None)

    def SetBasciInfo(self):
        """设置股票基本信息"""
        try:
            if not self.pro:
                # 如果还没初始化，先尝试初始化
                self.do_init()
            
            # 转换代码格式
            ts_code = self._convert_code_format(self.code)
            
            # 获取股票基本信息
            stock_basic = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,name,market,list_status')
            
            if not stock_basic.empty:
                self.name = stock_basic.iloc[0]['name']
                # 判断是否为股票（相对于指数）
                market = stock_basic.iloc[0]['market']
                self.is_stock = market in ['主板', '中小板', '创业板', '科创板', '北交所']
            else:
                # 如果查不到基本信息，根据代码判断
                self.name = ts_code
                self.is_stock = not (ts_code.startswith('000001.SH') or ts_code.startswith('399'))
                
        except Exception as e:
            # 如果获取基本信息失败，设置默认值
            self.name = self.code
            self.is_stock = True  # 默认认为是股票

    @classmethod
    def do_init(cls):
        """初始化tushare连接"""
        if not cls.is_connect:
            # 从环境变量获取token
            token = os.getenv('TUSHARE_TOKEN')
            if not token:
                raise Exception("请设置TUSHARE_TOKEN环境变量或在代码中设置token")
            
            ts.set_token(token)
            cls.pro = ts.pro_api()
            cls.is_connect = True

    @classmethod
    def do_close(cls):
        """关闭tushare连接"""
        if cls.is_connect:
            cls.pro = None
            cls.is_connect = None
